<?php
session_start();

// Include configuration
require_once 'config.php';

// Initialize variables
$success_message = '';
$error_message = '';
$form_data = [];

// Generate CSRF token
if (ENABLE_CSRF_PROTECTION && !isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

// Rate limiting check
if (RATE_LIMIT_ENABLED) {
    $client_ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    $rate_limit_key = 'rate_limit_' . md5($client_ip);

    if (!isset($_SESSION[$rate_limit_key])) {
        $_SESSION[$rate_limit_key] = ['count' => 0, 'timestamp' => time()];
    }

    $rate_data = $_SESSION[$rate_limit_key];

    // Reset counter if time window has passed
    if (time() - $rate_data['timestamp'] > RATE_LIMIT_WINDOW) {
        $_SESSION[$rate_limit_key] = ['count' => 0, 'timestamp' => time()];
        $rate_data = $_SESSION[$rate_limit_key];
    }

    // Check if rate limit exceeded
    if ($rate_data['count'] >= RATE_LIMIT_REQUESTS) {
        $error_message = 'Too many requests. Please wait a few minutes before trying again.';
    }
}

// Process form submission
if ($_POST && isset($_POST['submit_order']) && empty($error_message)) {
    // CSRF protection
    if (ENABLE_CSRF_PROTECTION) {
        if (!isset($_POST['csrf_token']) || !hash_equals($_SESSION['csrf_token'], $_POST['csrf_token'])) {
            $error_message = 'Security token mismatch. Please try again.';
        }
    }

    // Update rate limiting counter
    if (RATE_LIMIT_ENABLED && empty($error_message)) {
        $_SESSION[$rate_limit_key]['count']++;
    }

    if (empty($error_message)) {
        // Sanitize and validate input (using htmlspecialchars instead of deprecated FILTER_SANITIZE_STRING)
        $form_data = [
            'name' => trim(htmlspecialchars($_POST['name'] ?? '', ENT_QUOTES, 'UTF-8')),
            'email' => trim(filter_input(INPUT_POST, 'email', FILTER_SANITIZE_EMAIL)),
            'message' => trim(htmlspecialchars($_POST['message'] ?? '', ENT_QUOTES, 'UTF-8')),
            'phone' => trim(htmlspecialchars($_POST['phone'] ?? '', ENT_QUOTES, 'UTF-8')),
            'address' => trim(htmlspecialchars($_POST['address'] ?? '', ENT_QUOTES, 'UTF-8')),
            'city' => trim(htmlspecialchars($_POST['city'] ?? '', ENT_QUOTES, 'UTF-8')),
            'state' => trim(htmlspecialchars($_POST['state'] ?? '', ENT_QUOTES, 'UTF-8')),
            'zip' => trim(htmlspecialchars($_POST['zip'] ?? '', ENT_QUOTES, 'UTF-8')),
            'bags' => filter_input(INPUT_POST, 'bags', FILTER_VALIDATE_INT),
            'instructions' => trim(htmlspecialchars($_POST['instructions'] ?? '', ENT_QUOTES, 'UTF-8')),
            'payment' => trim(htmlspecialchars($_POST['payment'] ?? '', ENT_QUOTES, 'UTF-8'))
        ];
    
    // Validation
    $errors = [];
    
    if (empty($form_data['name'])) {
        $errors[] = 'Name is required';
    }
    
    if (empty($form_data['email']) || !filter_var($form_data['email'], FILTER_VALIDATE_EMAIL)) {
        $errors[] = 'Valid email is required';
    }
    
        if (empty($form_data['message'])) {
        $errors[] = 'Message is required';
    }

        if ($form_data['bags'] === false || $form_data['bags'] < 1 || $form_data['bags'] > MAX_BAGS_PER_ORDER) {
            $errors[] = "Number of bags must be between 1 and " . MAX_BAGS_PER_ORDER;
        }

        if (empty($errors)) {
            // Add timestamp
            $form_data['timestamp'] = date('Y-m-d H:i:s');
            $form_data['order_id'] = uniqid('ORDER_');

            // Try to save to Google Sheets
            $sheets_success = saveToGoogleSheet($form_data);

            // Try to send admin email notification
            $admin_email_success = sendEmailNotification($form_data, ADMIN_EMAIL);

            // Send customer confirmation email
            $customer_email_success = sendCustomerConfirmation($form_data);

            // Also save to local CSV as backup
            $csv_success = saveToSpreadsheet($form_data, ORDERS_DIRECTORY . ORDERS_CSV_FILE);

            // Determine success message based on results
            if ($sheets_success || $csv_success) {
                $success_message = MESSAGES['success'];
                if (!$customer_email_success) {
                    $success_message .= ' Note: We were unable to send you a confirmation email, but your order has been received.';
                }
                $form_data = []; // Clear form data on success
            } else {
                $error_message = 'There was an error saving your order. Please try again or contact us directly.';
            }
        }
    }
}

function saveToGoogleSheet($data) {
    try {
        $postData = [
            'order_data' => json_encode($data),
            'token' => GOOGLE_APP_TOKEN
        ];

        $options = [
            'http' => [
                'header'  => "Content-type: application/x-www-form-urlencoded\r\n",
                'method'  => 'POST',
                'content' => http_build_query($postData),
                'timeout' => 30
            ],
        ];

        $context = stream_context_create($options);
        $result = file_get_contents(GOOGLE_WEBAPP_URL, false, $context);

        // Check if the result indicates success
        if ($result !== false) {
            // Handle both JSON and plain text responses
            $response = json_decode($result, true);
            if ($response && isset($response['success'])) {
                return $response['success'];
            } else {
                // Handle plain text response from Apps Script
                return stripos($result, 'success') !== false;
            }
        }

        return false;
    } catch (Exception $e) {
        error_log("Google Sheets error: " . $e->getMessage());
        return false;
    }
}

function saveToSpreadsheet($data, $filename) {
    try {
        $file_exists = file_exists($filename);
        $file = fopen($filename, 'a');

        if (!$file) {
            return false;
        }

        // Add header row if file is new
        if (!$file_exists) {
            $headers = ['Order ID', 'Timestamp', 'Name', 'Email', 'Phone', 'Address', 'City', 'State', 'Zip', 'Bags', 'Payment Method', 'Message', 'Special Instructions'];
            fputcsv($file, $headers);
        }

        // Add data row
        $row = [
            $data['order_id'],
            $data['timestamp'],
            $data['name'],
            $data['email'],
            $data['phone'],
            $data['address'],
            $data['city'],
            $data['state'],
            $data['zip'],
            $data['bags'],
            $data['payment'],
            $data['message'],
            $data['instructions']
        ];

        $result = fputcsv($file, $row);
        fclose($file);

        return $result !== false;
    } catch (Exception $e) {
        error_log("CSV save error: " . $e->getMessage());
        return false;
    }
}

function sendCustomerConfirmation($data) {
    $subject = 'Order Confirmation - April\'s Apple Chips - ' . $data['order_id'];

    // Create HTML email for customer
    $html_message = "
    <html>
    <head>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .header { background: linear-gradient(135deg, #1C5D3B, #C33748); color: white; padding: 20px; text-align: center; }
            .content { padding: 20px; }
            .order-details { background: #f9f9f9; padding: 15px; border-radius: 5px; margin: 10px 0; }
            .label { font-weight: bold; color: #1C5D3B; }
            .footer { background: #f4f4f4; padding: 15px; text-align: center; font-size: 0.9em; color: #666; }
            .payment-info { background: #fff3cd; padding: 15px; border-radius: 5px; margin: 15px 0; border: 1px solid #ffc107; }
        </style>
    </head>
    <body>
        <div class='header'>
            <h2>🍎 Thank You for Your Apple Chip Order! 🍎</h2>
        </div>
        <div class='content'>
            <p>Dear {$data['name']},</p>
            <p>Thank you for your order! We've received your request for April's Apple Chips and are excited to prepare your delicious, carbon-negative snacks.</p>

            <div class='order-details'>
                <h3>Order Details:</h3>
                <p><span class='label'>Order ID:</span> {$data['order_id']}</p>
                <p><span class='label'>Order Date:</span> {$data['timestamp']}</p>
                <p><span class='label'>Bags Requested:</span> {$data['bags']}</p>
                <p><span class='label'>Preferred Payment Method:</span> {$data['payment']}</p>
                <p><span class='label'>Shipping Address:</span><br>
                   {$data['address']}<br>
                   {$data['city']}, {$data['state']} {$data['zip']}</p>
                <p><span class='label'>Your Message:</span><br>{$data['message']}</p>
                " . (!empty($data['instructions']) ? "<p><span class='label'>Special Instructions:</span><br>{$data['instructions']}</p>" : "") . "
            </div>

            <div class='payment-info'>
                <h3>Next Steps - Payment:</h3>
                <p>Your order will be processed once we receive payment. Please send payment for your {$data['bags']} bag(s) plus $7.50 shipping via your preferred method:</p>
                <ul>
                    <li><strong>PayPal:</strong> <a href='https://paypal.me/BBushwick'>paypal.me/BBushwick</a></li>
                    <li><strong>CashApp:</strong> <a href='https://cash.app/\$AppleChipKitchen'>cash.app/\$AppleChipKitchen</a></li>
                </ul>
                <p><small>Please include your name and order ID ({$data['order_id']}) with your payment.</small></p>
            </div>

            <p>We'll reach out if we have any questions about your order. Thank you for supporting our carbon-negative snack mission!</p>

            <p>Best regards,<br>
            April's Apple Chips Team</p>
        </div>
        <div class='footer'>
            <p>This is an automated confirmation. Please save this email for your records.</p>
        </div>
    </body>
    </html>";

    $headers = "From: " . FROM_EMAIL . "\r\n";
    $headers .= "Reply-To: " . ADMIN_EMAIL . "\r\n";
    $headers .= "MIME-Version: 1.0\r\n";
    $headers .= "Content-Type: text/html; charset=UTF-8\r\n";
    $headers .= "X-Mailer: PHP/" . phpversion() . "\r\n";

    return mail($data['email'], $subject, $html_message, $headers);
}

function sendEmailNotification($data, $admin_email) {
    $subject = 'New Apple Chip Order - ' . $data['order_id'];

    // Create HTML email
    $html_message = "
    <html>
    <head>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .header { background: #8B4513; color: white; padding: 20px; text-align: center; }
            .content { padding: 20px; }
            .order-details { background: #f9f9f9; padding: 15px; border-radius: 5px; margin: 10px 0; }
            .label { font-weight: bold; color: #8B4513; }
        </style>
    </head>
    <body>
        <div class='header'>
            <h2>🍎 New Apple Chip Order 🍎</h2>
        </div>
        <div class='content'>
            <div class='order-details'>
                <p><span class='label'>Order ID:</span> {$data['order_id']}</p>
                <p><span class='label'>Timestamp:</span> {$data['timestamp']}</p>
                <p><span class='label'>Name:</span> {$data['name']}</p>
                <p><span class='label'>Email:</span> {$data['email']}</p>
                <p><span class='label'>Phone:</span> {$data['phone']}</p>
                <p><span class='label'>Address:</span> {$data['address']}, {$data['city']}, {$data['state']} {$data['zip']}</p>
                <p><span class='label'>Bags Requested:</span> {$data['bags']}</p>
                <p><span class='label'>Payment Method:</span> {$data['payment']}</p>
                <p><span class='label'>Message:</span><br>{$data['message']}</p>
                <p><span class='label'>Special Instructions:</span><br>{$data['instructions']}</p>
            </div>
        </div>
    </body>
    </html>";

    $headers = "From: " . FROM_EMAIL . "\r\n";
    $headers .= "Reply-To: " . $data['email'] . "\r\n";
    $headers .= "MIME-Version: 1.0\r\n";
    $headers .= "Content-Type: text/html; charset=UTF-8\r\n";
    $headers .= "X-Mailer: PHP/" . phpversion() . "\r\n";

    return mail($admin_email, $subject, $html_message, $headers);
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>April's Apple Chips Holiday Orders</title>
    <link rel="shortcut icon" href="assets/favicon.ico" type="image/x-icon">
    <style>
        :root {
            --red: #C33748;
            --green: #1C5D3B;
            --black: #333;
            --white: #fff;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Georgia', serif;
            line-height: 1.6;
            color: var(--black);
            background: var(--red) url(Apple-Pile.png) no-repeat center/cover;
            background-attachment: fixed;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            position: relative;
        }

        .container::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.1);
            z-index: -1;
            pointer-events: none;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 30px 20px;
            background: linear-gradient(135deg, var(--green), var(--red));
            color: var(--white);
            border-radius: 15px;
            box-shadow: 0 8px 20px rgba(0,0,0,0.3);
            backdrop-filter: blur(10px);
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
            font-weight: bold;
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.95;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        }
        
        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            align-items: start;
        }
        
        .info-section {
            background: rgba(255, 255, 255, 0.95);
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 8px 20px rgba(0,0,0,0.2);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .form-section {
            background: rgba(255, 255, 255, 0.95);
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 8px 20px rgba(0,0,0,0.2);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .mobile-scroll-btn {
            display: none;
            background: var(--red);
            color: var(--white);
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            font-size: 1.1em;
            cursor: pointer;
            margin: 20px auto;
            transition: all 0.3s ease;
            box-shadow: 0 4px 10px rgba(195, 55, 72, 0.3);
        }

        .mobile-scroll-btn:hover {
            background: var(--green);
            transform: translateY(-2px);
            box-shadow: 0 6px 15px rgba(28, 93, 59, 0.4);
        }
        
        h2 {
            color: var(--green);
            margin-bottom: 20px;
            font-size: 1.8em;
            font-weight: bold;
        }

        h3 {
            color: var(--red);
            margin-bottom: 15px;
            font-size: 1.4em;
            font-weight: bold;
        }

        ul {
            margin-left: 20px;
            margin-bottom: 20px;
        }

        li {
            margin-bottom: 10px;
        }

        a {
            color: var(--red);
            text-decoration: none;
            font-weight: bold;
        }

        a:hover {
            color: var(--green);
            text-decoration: underline;
        }

        .form-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: var(--green);
        }
        
        input[type="text"],
        input[type="email"],
        input[type="tel"],
        input[type="number"],
        select,
        textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.9);
        }

        input:focus,
        select:focus,
        textarea:focus {
            outline: none;
            border-color: var(--red);
            background: var(--white);
            box-shadow: 0 0 10px rgba(195, 55, 72, 0.2);
        }

        textarea {
            resize: vertical;
            min-height: 100px;
        }

        .submit-btn {
            background: linear-gradient(135deg, var(--red), var(--green));
            color: var(--white);
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            font-size: 1.1em;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            box-shadow: 0 4px 15px rgba(195, 55, 72, 0.3);
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .submit-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(28, 93, 59, 0.4);
            background: linear-gradient(135deg, var(--green), var(--red));
        }
        
        .message {
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            font-weight: bold;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
        }

        .success {
            background: rgba(255, 255, 255, 0.95);
            color: var(--green);
            border: 2px solid var(--green);
            backdrop-filter: blur(10px);
        }

        .error {
            background: rgba(255, 255, 255, 0.95);
            color: var(--red);
            border: 2px solid var(--red);
            backdrop-filter: blur(10px);
        }

        .required {
            color: var(--red);
            font-weight: bold;
        }

        /* Video Section Styles */
        .video-section {
            background: rgba(255, 255, 255, 0.95);
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 8px 20px rgba(0,0,0,0.2);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            margin-bottom: 40px;
            text-align: center;
        }

        .video-container {
            position: relative;
            width: 100%;
            max-width: 800px;
            margin: 0 auto;
            padding-bottom: 56.25%; /* 16:9 aspect ratio */
            height: 0;
            overflow: hidden;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.3);
        }

        .video-container iframe {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border-radius: 10px;
        }

        .video-caption {
            margin-top: 15px;
            font-style: italic;
            color: var(--green);
            font-size: 1.1em;
        }

        /* Payment Widgets Styles */
        .payment-widgets {
            background: rgba(255, 255, 255, 0.95);
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 8px 20px rgba(0,0,0,0.2);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            margin-bottom: 40px;
        }

        .payment-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .payment-widget {
            text-align: center;
            padding: 20px;
            background: rgba(248, 249, 250, 0.8);
            border-radius: 10px;
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .payment-widget:hover {
            border-color: var(--red);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(195, 55, 72, 0.2);
        }

        .payment-btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            padding: 12px 24px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: bold;
            font-size: 1.1em;
            transition: all 0.3s ease;
            margin-bottom: 10px;
            min-width: 200px;
        }

        .paypal-btn {
            background: #0070ba;
            color: white;
        }

        .paypal-btn:hover {
            background: #005ea6;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 112, 186, 0.3);
        }

        .cashapp-btn {
            background: #00d632;
            color: white;
        }

        .cashapp-btn:hover {
            background: #00c12a;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 214, 50, 0.3);
        }

        .kofi-btn {
            background: #ff5f5f;
            color: white;
        }

        .kofi-btn:hover {
            background: #ff4757;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(255, 95, 95, 0.3);
        }

        .payment-icon {
            font-size: 1.2em;
        }

        .payment-note {
            font-size: 0.9em;
            color: var(--green);
            margin: 0;
            font-weight: bold;
        }

        .payment-instructions {
            background: rgba(28, 93, 59, 0.1);
            padding: 15px;
            border-radius: 10px;
            border-left: 4px solid var(--green);
            text-align: center;
        }

        .payment-instructions p {
            margin: 0;
            color: var(--green);
            font-weight: bold;
        }

        /* Additional Content Sections */
        .additional-content {
            margin-top: 40px;
        }

        .content-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            margin-bottom: 40px;
        }

        /* Make testimonial section span full width within grid */
        .content-grid .testimonial-section {
            grid-column: 1 / -1;
        }

        .content-section {
            background: rgba(255, 255, 255, 0.95);
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 8px 20px rgba(0,0,0,0.2);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        /* Impact Stats */
        .impact-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .stat-item {
            text-align: center;
            padding: 20px;
            background: linear-gradient(135deg, var(--green), var(--red));
            color: white;
            border-radius: 10px;
            box-shadow: 0 4px 10px rgba(0,0,0,0.2);
        }

        .stat-number {
            display: block;
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .stat-label {
            display: block;
            font-size: 0.9em;
            opacity: 0.9;
        }

        /* Process Steps */
        .process-steps {
            margin: 20px 0;
        }

        .step {
            display: flex;
            align-items: flex-start;
            margin-bottom: 20px;
            padding: 15px;
            background: rgba(248, 249, 250, 0.8);
            border-radius: 10px;
            transition: all 0.3s ease;
        }

        .step:hover {
            background: rgba(28, 93, 59, 0.1);
            transform: translateX(5px);
        }

        .step-number {
            background: var(--red);
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 1.2em;
            margin-right: 15px;
            flex-shrink: 0;
        }

        .step-content h4 {
            color: var(--green);
            margin-bottom: 5px;
            font-size: 1.1em;
        }

        .step-content p {
            margin: 0;
            color: var(--black);
            line-height: 1.5;
        }

        /* Features Grid */
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .feature {
            text-align: center;
            padding: 20px;
            background: rgba(248, 249, 250, 0.8);
            border-radius: 10px;
            transition: all 0.3s ease;
        }

        .feature:hover {
            background: rgba(195, 55, 72, 0.1);
            transform: translateY(-5px);
            box-shadow: 0 6px 15px rgba(0,0,0,0.1);
        }

        .feature-icon {
            font-size: 2.5em;
            margin-bottom: 10px;
            display: block;
        }

        .feature h4 {
            color: var(--green);
            margin-bottom: 10px;
            font-size: 1.1em;
        }

        .feature p {
            margin: 0;
            color: var(--black);
            font-size: 0.9em;
            line-height: 1.4;
        }

        /* Contact Info */
        .contact-methods {
            margin: 20px 0;
        }

        .contact-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: 20px;
            padding: 15px;
            background: rgba(248, 249, 250, 0.8);
            border-radius: 10px;
            transition: all 0.3s ease;
        }

        .contact-item:hover {
            background: rgba(28, 93, 59, 0.1);
            transform: translateX(5px);
        }

        .contact-icon {
            font-size: 1.5em;
            margin-right: 15px;
            flex-shrink: 0;
        }

        .contact-details strong {
            color: var(--green);
            display: block;
            margin-bottom: 5px;
            font-size: 1.1em;
        }

        .contact-details p {
            margin: 0;
            color: var(--black);
            line-height: 1.4;
        }

        /* Testimonial Slider Styles */
        .testimonial-slider {
            margin: 20px 0;
            position: relative;
        }

        .testimonial-container {
            overflow: hidden;
            border-radius: 15px;
            background: rgba(248, 249, 250, 0.9);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .testimonial-track {
            display: flex;
            transition: transform 0.5s ease;
        }

        .testimonial {
            min-width: 100%;
            padding: 30px;
            position: relative;
            background: rgba(248, 249, 250, 0.9);
            border-left: 4px solid var(--red);
        }

        .testimonial.negative {
            border-left-color: var(--green);
            background: rgba(255, 248, 220, 0.9);
        }

        .testimonial::before {
            content: '"';
            position: absolute;
            top: -5px;
            left: 20px;
            font-size: 4em;
            color: var(--red);
            opacity: 0.3;
            font-family: Georgia, serif;
            line-height: 1;
        }

        .testimonial.negative::before {
            color: var(--green);
        }

        .testimonial-content {
            margin-bottom: 15px;
        }

        .testimonial-content p {
            margin: 0;
            font-style: italic;
            color: var(--black);
            line-height: 1.6;
            font-size: 1.1em;
            position: relative;
            z-index: 1;
        }

        .testimonial-rating {
            text-align: right;
            font-size: 1.2em;
            margin-top: 10px;
        }

        .slider-controls {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 20px;
            margin-top: 20px;
        }

        .slider-btn {
            background: var(--red);
            color: white;
            border: none;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            font-size: 1.5em;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .slider-btn:hover {
            background: var(--green);
            transform: scale(1.1);
        }

        .slider-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }

        .slider-dots {
            display: flex;
            gap: 10px;
        }

        .dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #ccc;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .dot.active {
            background: var(--red);
            transform: scale(1.2);
        }

        .dot:hover {
            background: var(--green);
        }

        /* DIY Section Styles */
        .diy-content {
            text-align: center;
        }

        .diy-icon {
            margin: 20px 0;
            opacity: 0.8;
        }

        .diy-benefits {
            background: rgba(28, 93, 59, 0.1);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: left;
        }

        .diy-benefits h4 {
            color: var(--green);
            margin-bottom: 15px;
            text-align: center;
        }

        .diy-benefits ul {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .diy-benefits li {
            padding: 8px 0;
            border-bottom: 1px solid rgba(28, 93, 59, 0.2);
        }

        .diy-benefits li:last-child {
            border-bottom: none;
        }

        .diy-cta {
            margin-top: 25px;
        }

        .diy-btn {
            display: inline-block;
            background: linear-gradient(135deg, var(--green), var(--red));
            color: white;
            padding: 15px 30px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: bold;
            font-size: 1.1em;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(28, 93, 59, 0.3);
            margin-bottom: 10px;
        }

        .diy-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(195, 55, 72, 0.4);
            background: linear-gradient(135deg, var(--red), var(--green));
            color: white;
            text-decoration: none;
        }

        .diy-note {
            font-size: 0.9em;
            color: var(--green);
            font-style: italic;
            margin-top: 10px;
        }

        /* Enhanced Info Section Styles */
        .story-section {
            display: flex;
            align-items: center;
            gap: 20px;
            margin: 25px 0;
            padding: 20px;
            background: rgba(28, 93, 59, 0.05);
            border-radius: 10px;
            border-left: 4px solid var(--green);
        }

        .story-highlight {
            text-align: center;
            min-width: 120px;
            flex-shrink: 0;
        }

        .big-number {
            display: block;
            font-size: 2.5em;
            font-weight: bold;
            color: var(--red);
            line-height: 1;
            margin-bottom: 5px;
        }

        .highlight-text {
            display: block;
            font-size: 0.9em;
            color: var(--green);
            font-weight: bold;
            line-height: 1.2;
        }

        .story-content p {
            margin: 0;
            font-size: 1.1em;
            line-height: 1.6;
            color: var(--black);
        }

        .mission-box {
            background: linear-gradient(135deg, rgba(28, 93, 59, 0.1), rgba(195, 55, 72, 0.1));
            padding: 25px;
            border-radius: 15px;
            margin: 25px 0;
            border: 2px solid rgba(28, 93, 59, 0.2);
        }

        .mission-box h3 {
            margin-bottom: 15px;
            color: var(--green);
        }

        .mission-box p {
            margin: 0;
            font-size: 1.1em;
            line-height: 1.6;
        }

        .fun-fact {
            background: rgba(195, 55, 72, 0.1);
            padding: 20px;
            border-radius: 10px;
            margin: 25px 0;
            border-left: 4px solid var(--red);
            font-style: italic;
        }

        .fun-fact p {
            margin: 0;
            font-size: 1.1em;
            line-height: 1.6;
        }

        .terms-section {
            margin: 30px 0;
        }

        .terms-section h3 {
            color: var(--green);
            margin-bottom: 20px;
            text-align: center;
        }

        .terms-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
        }

        .term-item {
            display: flex;
            align-items: flex-start;
            gap: 15px;
            padding: 15px;
            background: rgba(248, 249, 250, 0.8);
            border-radius: 10px;
            transition: all 0.3s ease;
        }

        .term-item:hover {
            background: rgba(28, 93, 59, 0.1);
            transform: translateY(-2px);
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
        }

        .term-icon {
            font-size: 1.5em;
            flex-shrink: 0;
            margin-top: 2px;
        }

        .term-content strong {
            display: block;
            color: var(--green);
            margin-bottom: 5px;
            font-size: 1em;
        }

        .term-content p {
            margin: 0;
            font-size: 0.9em;
            color: var(--black);
            line-height: 1.4;
        }

        /* Heartwarmers Section Styles */
        .heartwarmers-content {
            display: flex;
            align-items: flex-start;
            gap: 30px;
            margin: 20px 0;
        }

        .heartwarmers-logo {
            flex-shrink: 0;
            text-align: center;
        }

        .heartwarmers-img {
            max-width: 150px;
            height: auto;
            border-radius: 10px;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
        }

        .heartwarmers-info {
            flex: 1;
        }

        .heartwarmers-info p {
            font-size: 1.1em;
            line-height: 1.6;
            margin-bottom: 20px;
        }

        .heartwarmers-features {
            margin: 20px 0;
        }

        .feature-point {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 10px;
            font-size: 1em;
        }

        .feature-point .feature-icon {
            font-size: 1.2em;
        }

        .heartwarmers-cta {
            margin-top: 25px;
        }

        .heartwarmers-btn {
            display: inline-block;
            background: linear-gradient(135deg, var(--red), var(--green));
            color: white;
            padding: 12px 25px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(195, 55, 72, 0.3);
        }

        .heartwarmers-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(28, 93, 59, 0.4);
            background: linear-gradient(135deg, var(--green), var(--red));
            color: white;
            text-decoration: none;
        }

        .heartwarmers-note {
            font-size: 0.9em;
            color: var(--green);
            font-style: italic;
            margin-top: 10px;
        }

        /* Chat Widget Info Styles */
        .chat-widget-info {
            display: flex;
            align-items: center;
            gap: 20px;
            background: rgba(28, 93, 59, 0.1);
            padding: 25px;
            border-radius: 15px;
            border-left: 4px solid var(--green);
        }

        .chat-icon {
            font-size: 3em;
            flex-shrink: 0;
        }

        .chat-content h3 {
            color: var(--green);
            margin-bottom: 10px;
        }

        .chat-content p {
            margin-bottom: 10px;
            line-height: 1.6;
        }

        .chat-note {
            font-size: 0.9em;
            color: var(--green);
            font-style: italic;
        }

        /* Blog Section Styles */
        .blog-content {
            display: flex;
            align-items: flex-start;
            gap: 30px;
            margin: 20px 0;
        }

        .blog-icon {
            flex-shrink: 0;
            text-align: center;
            opacity: 0.8;
        }

        .blog-info {
            flex: 1;
        }

        .blog-info p {
            font-size: 1.1em;
            line-height: 1.6;
            margin-bottom: 20px;
        }

        .blog-features {
            margin: 20px 0;
        }

        .blog-feature {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 10px;
            font-size: 1em;
        }

        .blog-feature .feature-icon {
            font-size: 1.2em;
        }

        .blog-cta {
            margin-top: 25px;
        }

        .blog-btn {
            display: inline-block;
            background: linear-gradient(135deg, var(--green), var(--red));
            color: white;
            padding: 12px 25px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(28, 93, 59, 0.3);
        }

        .blog-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(195, 55, 72, 0.4);
            background: linear-gradient(135deg, var(--red), var(--green));
            color: white;
            text-decoration: none;
        }

        .blog-note {
            font-size: 0.9em;
            color: var(--green);
            font-style: italic;
            margin-top: 10px;
        }

        /* Linktree Navigation Styles */
        .linktree-navigation {
            margin-top: 40px;
            padding: 0 20px 40px 20px;
        }

        .linktree-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
            width: 100%;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px 30px;
            background: linear-gradient(135deg, var(--green), var(--red));
            color: white;
            text-decoration: none;
            border-radius: 15px;
            font-size: 1.3em;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 6px 20px rgba(28, 93, 59, 0.3);
            position: relative;
            overflow: hidden;
        }

        .linktree-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s ease;
        }

        .linktree-btn:hover::before {
            left: 100%;
        }

        .linktree-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(195, 55, 72, 0.4);
            background: linear-gradient(135deg, var(--red), var(--green));
            color: white;
            text-decoration: none;
        }

        .nav-icon {
            font-size: 1.5em;
            flex-shrink: 0;
        }

        .nav-text {
            flex: 1;
            text-align: center;
            position: relative;
            z-index: 1;
        }

        .nav-arrow {
            font-size: 1.5em;
            flex-shrink: 0;
            transition: transform 0.3s ease;
        }

        .linktree-btn:hover .nav-arrow {
            transform: translateX(5px);
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .mobile-scroll-btn {
                display: block;
            }

            .header h1 {
                font-size: 2em;
            }

            .container {
                padding: 10px;
            }

            .info-section,
            .form-section {
                padding: 20px;
            }

            /* Mobile styles for new sections */
            .video-section,
            .payment-widgets,
            .content-section {
                padding: 20px;
            }

            .payment-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .content-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .impact-stats {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .features-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .testimonial {
                padding: 20px;
            }

            .testimonial::before {
                font-size: 3em;
                top: -2px;
                left: 15px;
            }

            .slider-controls {
                gap: 15px;
            }

            .slider-btn {
                width: 35px;
                height: 35px;
                font-size: 1.3em;
            }

            /* Mobile styles for enhanced info section */
            .story-section {
                flex-direction: column;
                text-align: center;
                gap: 15px;
            }

            .story-highlight {
                min-width: auto;
            }

            .big-number {
                font-size: 2em;
            }

            .terms-grid {
                grid-template-columns: 1fr;
                gap: 10px;
            }

            .term-item {
                padding: 12px;
            }

            .mission-box,
            .fun-fact {
                padding: 15px;
            }

            /* Mobile styles for new sections */
            .heartwarmers-content,
            .blog-content {
                flex-direction: column;
                text-align: center;
                gap: 20px;
            }

            .heartwarmers-img {
                max-width: 120px;
            }

            .chat-widget-info {
                flex-direction: column;
                text-align: center;
                gap: 15px;
                padding: 20px;
            }

            .chat-icon {
                font-size: 2.5em;
            }

            /* Mobile styles for linktree navigation */
            .linktree-navigation {
                padding: 0 10px 30px 10px;
            }

            .linktree-btn {
                padding: 18px 20px;
                font-size: 1.1em;
                gap: 12px;
            }

            .nav-icon,
            .nav-arrow {
                font-size: 1.3em;
            }

            .stat-number {
                font-size: 1.8em;
            }

            .step {
                flex-direction: column;
                text-align: center;
            }

            .step-number {
                margin-right: 0;
                margin-bottom: 10px;
            }

            .contact-item {
                flex-direction: column;
                text-align: center;
            }

            .contact-icon {
                margin-right: 0;
                margin-bottom: 10px;
            }

            .video-container {
                padding-bottom: 75%; /* Adjust aspect ratio for mobile */
            }
        }
    </style>
            <!-- Chatway -->
    <script id="chatway" async="true" src="https://cdn.chatway.app/widget.js?id=QGxjxq7wNQi8"></script>
    <!-- Clarity -->
    <script type="text/javascript">
        (function(c,l,a,r,i,t,y){
            c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
            t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
            y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
        })(window, document, "clarity", "script", "l6wg07j3fq");
    </script>

</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🍎 Merry Chip-Mas! 🍎</h1>
            <p>Order April's Apple Chips for the perfect holiday snack!</p>
        </div>

        <!-- Promotional Video Section -->
        <div class="video-section">
            <h2>🎬 See Our Apple Chips in Action!</h2>
            <div class="video-container">
                <iframe
                    src="chipmas advert.mp4"
                    title="April's Apple Chips - Behind the Scenes"
                    frameborder="0"
                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                    allowfullscreen>
                </iframe>
                <p class="video-caption">Watch how we transform rescued apples into delicious, carbon-negative snacks!</p>
            </div>
        </div>

        <!-- Payment Widgets Section -->
        <div class="payment-widgets">
            <h2>💳 Quick Payment Options</h2>
            <div class="payment-grid">
                <div class="payment-widget">
                    <h3>PayPal</h3>
                    <div class="paypal-widget">
                        <a href="https://paypal.me/BBushwick" target="_blank" rel="noopener" class="payment-btn paypal-btn">
                            <span class="payment-icon">💳</span>
                            Pay with PayPal
                        </a>
                        <p class="payment-note">Send to: paypal.me/BBushwick</p>
                    </div>
                </div>

                <div class="payment-widget">
                    <h3>CashApp</h3>
                    <div class="cashapp-widget">
                        <a href="https://cash.app/$AppleChipKitchen" target="_blank" rel="noopener" class="payment-btn cashapp-btn">
                            <span class="payment-icon">💰</span>
                            Pay with CashApp
                        </a>
                        <p class="payment-note">Send to: $AppleChipKitchen</p>
                    </div>
                </div>

                <div class="payment-widget">
                    <h3>Ko-fi</h3>
                    <div class="kofi-widget">
                        <a href="https://ko-fi.com/aprilsapplechips" target="_blank" rel="noopener" class="payment-btn kofi-btn">
                            <span class="payment-icon">☕</span>
                            Support on Ko-fi
                        </a>
                        <p class="payment-note">Buy us a coffee & chips!</p>
                    </div>
                </div>
            </div>
            <div class="payment-instructions">
                <p><strong>💡 Payment Instructions:</strong> After placing your order below, use any of these methods to send payment. Please include your name and order ID with your payment!</p>
            </div>
        </div>
        
        <?php if ($success_message): ?>
            <div class="message success"><?php echo htmlspecialchars($success_message); ?></div>
        <?php endif; ?>
        
        <?php if ($error_message): ?>
            <div class="message error"><?php echo $error_message; ?></div>
        <?php endif; ?>
        
        <div class="main-content">
            <div class="info-section">
                <h2>🍎 Behind the Chips...</h2>

                <div class="mission-box">
                    <h3>🌱 Our Mission</h3>
                    <p>These are apples passed along from grocers and orchards that didn't make it to market. Without secondhand sources, these apples would rot into greenhouse warming gases. <strong>That's why April's Apple Chips are carbon negative!</strong></p>
                </div>

                <div class="fun-fact">
                    <p>💰 My process is open source for enthusiasts who want to make their own. It's not that lucrative or scalable, but it's its own form of money - something I call <strong>'Chiptocurrency'</strong> 😁</p>
                </div>

                <div class="terms-section">
                    <h3>📋 Order Information</h3>
                    <div class="terms-grid">
                        <div class="term-item">
                            <div class="term-icon">🏃‍♂️</div>
                            <div class="term-content">
                                <strong>First Come, First Served</strong>
                                <p>Orders filled in order received</p>
                            </div>
                        </div>
                        <div class="term-item">
                            <div class="term-icon">🛍️</div>
                            <div class="term-content">
                                <strong>10 Bag Limit</strong>
                                <p>Max per customer to ensure availability</p>
                            </div>
                        </div>
                        <div class="term-item">
                            <div class="term-icon">📦</div>
                            <div class="term-content">
                                <strong>$7.50 Shipping</strong>
                                <p>Flat rate regardless of bag count</p>
                            </div>
                        </div>
                        <div class="term-item">
                            <div class="term-icon">💳</div>
                            <div class="term-content">
                                <strong>Payment Required</strong>
                                <p>PayPal or CashApp after order submission</p>
                            </div>
                        </div>
                        <div class="term-item">
                            <div class="term-icon">🎁</div>
                            <div class="term-content">
                                <strong>Holiday Guarantee</strong>
                                <p>Refund/discount for late holiday gifts</p>
                            </div>
                        </div>
                        <div class="term-item">
                            <div class="term-icon">📝</div>
                            <div class="term-content">
                                <strong>Special Requests</strong>
                                <p>Add dietary restrictions in notes</p>
                            </div>
                        </div>
                    </div>
                </div>

                <button class="mobile-scroll-btn" onclick="scrollToForm()">📝 Place Your Order</button>
            </div>
            
            <div class="form-section" id="order-form">
                <h2>Apple Chip Order Request</h2>
                <form method="post" action="">
                    <?php if (ENABLE_CSRF_PROTECTION): ?>
                        <input type="hidden" name="csrf_token" value="<?php echo htmlspecialchars($_SESSION['csrf_token']); ?>">
                    <?php endif; ?>
                    <div class="form-group">
                        <label for="name">Your Name <span class="required">*</span></label>
                        <input type="text" id="name" name="name" required value="<?php echo htmlspecialchars($form_data['name'] ?? ''); ?>">
                    </div>
                    
                    <div class="form-group">
                        <label for="email">Your Email <span class="required">*</span></label>
                        <input type="email" id="email" name="email" required value="<?php echo htmlspecialchars($form_data['email'] ?? ''); ?>">
                    </div>
                    
                    <div class="form-group">
                        <label for="message">Your Message <span class="required">*</span></label>
                        <textarea id="message" name="message" required placeholder="Tell us about your order..."><?php echo htmlspecialchars($form_data['message'] ?? ''); ?></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label for="phone">Phone Number</label>
                        <input type="tel" id="phone" name="phone" value="<?php echo htmlspecialchars($form_data['phone'] ?? ''); ?>">
                    </div>
                    
                    <div class="form-group">
                        <label for="address">Mailing Address</label>
                        <input type="text" id="address" name="address" value="<?php echo htmlspecialchars($form_data['address'] ?? ''); ?>">
                    </div>
                    
                    <div class="form-group">
                        <label for="city">City</label>
                        <input type="text" id="city" name="city" value="<?php echo htmlspecialchars($form_data['city'] ?? ''); ?>">
                    </div>
                    
                    <div class="form-group">
                        <label for="state">State</label>
                        <input type="text" id="state" name="state" value="<?php echo htmlspecialchars($form_data['state'] ?? ''); ?>">
                    </div>
                    
                    <div class="form-group">
                        <label for="zip">Postal Zip</label>
                        <input type="text" id="zip" name="zip" value="<?php echo htmlspecialchars($form_data['zip'] ?? ''); ?>">
                    </div>
                    
                    <div class="form-group">
                        <label for="bags">How Many Bags? <span class="required">*</span> (Max: <?php echo MAX_BAGS_PER_ORDER; ?>)</label>
                        <input type="number" id="bags" name="bags" min="1" max="<?php echo MAX_BAGS_PER_ORDER; ?>" step="1" required value="<?php echo htmlspecialchars($form_data['bags'] ?? ''); ?>">
                    </div>
                    
                    <div class="form-group">
                        <label for="instructions">Special Instructions</label>
                        <textarea id="instructions" name="instructions" placeholder="Any special requests or dietary restrictions..."><?php echo htmlspecialchars($form_data['instructions'] ?? ''); ?></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label for="payment">Payment Method <span class="required">*</span></label>
                        <select id="payment" name="payment" required>
                            <option value="">Select payment method...</option>
                            <option value="PayPal" <?php echo (($form_data['payment'] ?? '') === 'PayPal') ? 'selected' : ''; ?>>PayPal</option>
                            <option value="CashApp" <?php echo (($form_data['payment'] ?? '') === 'CashApp') ? 'selected' : ''; ?>>CashApp</option>
                            <option value="Ko-fi" <?php echo (($form_data['payment'] ?? '') === 'Ko-fi') ? 'selected' : ''; ?>>Ko-fi</option>
                        </select>
                    </div>
                    
                    <button type="submit" name="submit_order" class="submit-btn">🍎 Place Order 🍎</button>
                </form>
            </div>
        </div>

        <!-- Testimonials Section - Right after order form -->
        <div class="content-section testimonial-section">
            <h2>💬 What Our Customers Say</h2>
            <div class="testimonial-slider">
                <div class="testimonial-container">
                    <div class="testimonial-track" id="testimonialTrack">
                        <!-- Testimonials will be loaded here via JavaScript -->
                    </div>
                </div>
                <div class="slider-controls">
                    <button class="slider-btn prev-btn" id="prevBtn">‹</button>
                    <div class="slider-dots" id="sliderDots"></div>
                    <button class="slider-btn next-btn" id="nextBtn">›</button>
                </div>
            </div>
        </div>

        <!-- Additional Content Sections -->
        <div class="additional-content">
            <div class="content-grid">
                <div class="content-section">
                    <h2>🌱 Our Environmental Impact</h2>
                    <div class="impact-stats">
                        <div class="stat-item">
                            <span class="stat-number">5,000+</span>
                            <span class="stat-label">Pounds of Apples Rescued</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">3,000+</span>
                            <span class="stat-label">Bags Distributed</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">100%</span>
                            <span class="stat-label">Carbon Negative</span>
                        </div>
                    </div>
                    <p>Every bag of April's Apple Chips represents rescued food that would have otherwise contributed to greenhouse gases in landfills. By choosing our chips, you're not just getting a delicious snack – you're actively participating in environmental conservation!</p>
                </div>

                <div class="content-section">
                    <h2>🍎 The Apple Rescue Process</h2>
                    <div class="process-steps">
                        <div class="step">
                            <div class="step-number">1</div>
                            <div class="step-content">
                                <h4>Apple Recovery</h4>
                                <p>We partner with local grocers and orchards to rescue apples that didn't make it to market or weren't sold in time.</p>
                            </div>
                        </div>
                        <div class="step">
                            <div class="step-number">2</div>
                            <div class="step-content">
                                <h4>Careful Selection</h4>
                                <p>Each apple is hand-selected and inspected to ensure only the best quality fruit makes it into our chips.</p>
                            </div>
                        </div>
                        <div class="step">
                            <div class="step-number">3</div>
                            <div class="step-content">
                                <h4>Artisan Dehydration</h4>
                                <p>Using our open-source process, we carefully dehydrate the apples to preserve their natural sweetness and nutrition.</p>
                            </div>
                        </div>
                        <div class="step">
                            <div class="step-number">4</div>
                            <div class="step-content">
                                <h4>Community Distribution</h4>
                                <p>The finished chips are packaged and shared with our community, turning waste into wonderful snacks!</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="content-grid">
                <div class="content-section">
                    <h2>🏆 All that, and a Bag of Chips!</h2>
                    <div class="features-grid">
                        <div class="feature">
                            <div class="feature-icon">🌍</div>
                            <h4>Carbon Negative</h4>
                            <p>Every bag prevents food waste and methane rot.</p>
                        </div>
                        <div class="feature">
                            <div class="feature-icon">🤝</div>
                            <h4>Community Focused</h4>
                            <p>A way to tackle food insecurity with local recovered apples.</p>
                        </div>
                        <div class="feature">
                            <div class="feature-icon">📖</div>
                            <h4>Open Source</h4>
                            <p>Our secret sauce is there for anyone who wants to make their own at home.</p>
                        </div>
                        <div class="feature">
                            <div class="feature-icon">❤️</div>
                            <h4>Made with Love</h4>
                            <p>Each batch is carefully crafted by hand with attention to quality and taste.</p>
                        </div>
                    </div>
                </div>

                <div class="content-section">
                    <h2>🔨 Make Your Own Apple Chips!</h2>
                    <div class="diy-content">
                        <div class="diy-icon">
                            <span style="font-size: 3em;">🍎➡️🔥➡️🥨</span>
                        </div>
                        <p>Don't want to wait for an order? Want to try making your own carbon-negative snacks? Our process is completely open source!</p>

                        <div class="diy-benefits">
                            <h4>Why Make Your Own?</h4>
                            <ul>
                                <li>🌱 <strong>Rescue local apples</strong> from your area</li>
                                <li>⚡ <strong>Start immediately</strong> - no waiting for shipping</li>
                                <li>🎓 <strong>Learn the process</strong> and share with others</li>
                                <li>💰 <strong>Cost-effective</strong> for large quantities</li>
                                <li>🎨 <strong>Customize flavors</strong> and techniques</li>
                            </ul>
                        </div>

                        <div class="diy-cta">
                            <a href="diy-guide.html" class="diy-btn">
                                📖 Read the Do It Yourself Guide to Dehydrating Fruit
                            </a>
                            <p class="diy-note">Complete step-by-step instructions, equipment lists, and pro tips!</p>
                        </div>
                    </div>
                </div>

                <div class="content-section">
                    <h2>� Other Projects You are Supporting with your Purchase</h2>
                    <div class="heartwarmers-content">
                        <div class="heartwarmers-logo">
                            <!-- Add your Heartwarmers logo here -->
                            <img src="hw-logo.png" alt="Heartwarmers Project" class="heartwarmers-img">
                        </div>
                        <div class="heartwarmers-info">
                            <p>When you purchase April's Apple Chips, you're not just getting a delicious snack - you're also supporting the <strong>Heartwarmers Project</strong>, another initiative focused on community impact and sustainability.</p>

                            <div class="heartwarmers-cta">
                                <a href="https://www.aachips.co/heartwarmers" target="_blank" rel="noopener" class="heartwarmers-btn">
                                    🔗 Learn More About Heartwarmers
                                </a>
                                <p class="heartwarmers-note">See how your apple chip purchase supports this amazing project!</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="content-section">
                    <h2>�📞 Get in Touch</h2>
                    <div class="contact-info">
                        <p>Have questions, concerns, or grievances? We'd love to hear from you!</p>
                        <div class="chat-widget-info">
                            <div class="chat-icon">�</div>
                            <div class="chat-content">
                                <h3>Use the Chat Widget!</h3>
                                <p>Have questions about our apple chips or want to learn more about our process? Use the <strong>chat widget in the bottom right corner</strong>. You'll get an email back at our earliest convenience.</p>
                                <p class="chat-note">We're here to help with orders, ingredients, shipping, and anything else you'd like to know.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="content-section">
                    <h2>📝 Apple Chip Blog</h2>
                    <div class="blog-content">
                        <div class="blog-icon">
                            <span style="font-size: 3em;">📖</span>
                        </div>
                        <div class="blog-info">
                            <p>Where I share content and writings related to anything and everything under the Sun of Apple Chips. Enter at your own risk.</p>

                            <div class="blog-cta">
                                <a href="https://www.aachips.co/obsidian" target="_blank" rel="noopener" class="blog-btn">
                                    📖 Visit the Apple Chip Blog
                                </a>
                                <p class="blog-note">Discover cool facts and stories!</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Full-width navigation back to linktree -->
        <div class="linktree-navigation">
            <a href="https://www.aachips.co" class="linktree-btn">
                <span class="nav-icon">🏠</span>
                <span class="nav-text">Back to AAChips Linktree</span>
                <span class="nav-arrow">→</span>
            </a>
        </div>
    </div>

    <script>
        function scrollToForm() {
            document.getElementById('order-form').scrollIntoView({
                behavior: 'smooth'
            });
        }

        // Auto-scroll to form if there are errors (to show error message)
        <?php if ($error_message && !empty($_POST)): ?>
        setTimeout(() => {
            scrollToForm();
        }, 100);
        <?php endif; ?>

        // Testimonial Slider Functionality
        let currentTestimonial = 0;
        let testimonials = [];

        // Load testimonials from JSON
        async function loadTestimonials() {
            try {
                const response = await fetch('testimonials.json');
                const data = await response.json();

                // Sort testimonials to prioritize highlighted ones
                testimonials = data.testimonials.sort((a, b) => {
                    // Highlighted testimonials come first
                    if (a.highlight && !b.highlight) return -1;
                    if (!a.highlight && b.highlight) return 1;
                    // If both or neither are highlighted, maintain original order
                    return 0;
                });

                renderTestimonials();
                setupSlider();
            } catch (error) {
                console.error('Error loading testimonials:', error);
                // Fallback testimonials if JSON fails to load
                testimonials = [
                    {
                        text: "These apple chips are amazing! They are the perfect healthy snack for my busy lifestyle.",
                        rating: 5,
                        type: "positive",
                        highlight: true
                    },
                    {
                        text: "These were the worst snack I have ever had. I can't believe I ate the entire bag. Never again.",
                        rating: 1,
                        type: "negative",
                        highlight: true
                    }
                ];
                renderTestimonials();
                setupSlider();
            }
        }

        function renderTestimonials() {
            const track = document.getElementById('testimonialTrack');
            const dotsContainer = document.getElementById('sliderDots');

            track.innerHTML = '';
            dotsContainer.innerHTML = '';

            testimonials.forEach((testimonial, index) => {
                // Create testimonial element
                const testimonialEl = document.createElement('div');
                testimonialEl.className = `testimonial ${testimonial.type}`;

                const stars = '⭐'.repeat(testimonial.rating);

                testimonialEl.innerHTML = `
                    <div class="testimonial-content">
                        <p>"${testimonial.text}"</p>
                    </div>
                    <div class="testimonial-rating">${stars}</div>
                `;

                track.appendChild(testimonialEl);

                // Create dot
                const dot = document.createElement('div');
                dot.className = `dot ${index === 0 ? 'active' : ''}`;
                dot.addEventListener('click', () => goToTestimonial(index));
                dotsContainer.appendChild(dot);
            });
        }

        function setupSlider() {
            const prevBtn = document.getElementById('prevBtn');
            const nextBtn = document.getElementById('nextBtn');

            prevBtn.addEventListener('click', () => {
                currentTestimonial = currentTestimonial > 0 ? currentTestimonial - 1 : testimonials.length - 1;
                updateSlider();
            });

            nextBtn.addEventListener('click', () => {
                currentTestimonial = currentTestimonial < testimonials.length - 1 ? currentTestimonial + 1 : 0;
                updateSlider();
            });

            // Auto-advance slider every 8 seconds
            setInterval(() => {
                currentTestimonial = currentTestimonial < testimonials.length - 1 ? currentTestimonial + 1 : 0;
                updateSlider();
            }, 8000);
        }

        function goToTestimonial(index) {
            currentTestimonial = index;
            updateSlider();
        }

        function updateSlider() {
            const track = document.getElementById('testimonialTrack');
            const dots = document.querySelectorAll('.dot');

            track.style.transform = `translateX(-${currentTestimonial * 100}%)`;

            dots.forEach((dot, index) => {
                dot.classList.toggle('active', index === currentTestimonial);
            });
        }

        // Initialize testimonials when page loads
        document.addEventListener('DOMContentLoaded', loadTestimonials);
    </script>
</body>
</html>
